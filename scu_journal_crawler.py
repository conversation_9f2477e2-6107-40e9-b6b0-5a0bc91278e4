#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四川大学期刊查询系统爬虫 - 最终版本
基于页面结构分析的优化版本
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import re
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import warnings
warnings.filterwarnings('ignore')


class SCUJournalCrawler:
    """四川大学期刊查询系统爬虫类 - 优化版"""
    
    def __init__(self):
        self.base_url = "https://ir.scu.edu.cn"
        self.login_url = "https://ir.scu.edu.cn/style/excel/views/index3.jsp"
        self.driver = None
        self.journals_data = []
        
    def setup_driver(self, headless=False):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            print("✓ Chrome浏览器驱动设置成功")
            return True
        except Exception as e:
            print(f"✗ Chrome浏览器驱动设置失败: {e}")
            print("请确保已安装Chrome浏览器和ChromeDriver")
            return False
    
    def login(self, username, password):
        """登录系统"""
        try:
            print("正在访问登录页面...")
            self.driver.get(self.login_url)
            time.sleep(3)
            
            print("正在查找登录表单...")
            
            # 查找用户名输入框
            username_input = self.driver.find_element(By.ID, "userName")
            print("✓ 找到用户名输入框")
            
            # 查找密码输入框
            password_input = self.driver.find_element(By.ID, "password")
            print("✓ 找到密码输入框")
            
            # 输入用户名和密码
            print("正在输入登录信息...")
            username_input.clear()
            username_input.send_keys(username)
            password_input.clear()
            password_input.send_keys(password)
            
            # 检查验证码
            try:
                captcha_input = self.driver.find_element(By.ID, "randomCode")
                print("✓ 检测到验证码输入框")
                print("请在浏览器中输入验证码...")
                input("输入验证码后按回车继续...")
            except NoSuchElementException:
                print("未检测到验证码")
            
            # 点击登录按钮
            login_button = self.driver.find_element(By.ID, "loginbtn1")
            print("✓ 找到登录按钮，正在登录...")
            login_button.click()
            
            # 等待登录完成
            time.sleep(5)
            
            # 检查登录结果
            current_url = self.driver.current_url
            if "login" not in current_url.lower():
                print("✓ 登录成功")
                return True
            else:
                print("登录状态不确定，继续尝试...")
                return True
                
        except Exception as e:
            print(f"✗ 登录失败: {e}")
            print("请手动完成登录...")
            input("完成登录后按回车继续...")
            return True
    
    def wait_for_data_load(self):
        """等待数据加载"""
        try:
            # 等待layui表格加载完成
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".layui-table tbody tr"))
            )
            print("✓ layui表格数据加载完成")

            # 额外等待确保数据完全加载
            time.sleep(3)
            return True
        except TimeoutException:
            print("⚠ 表格数据加载超时，但继续尝试...")
            return False
    
    def parse_journal_data(self):
        """解析期刊数据 - 基于实际HTML结构"""
        journals = []
        try:
            # 保存页面源码用于调试
            with open('current_page.html', 'w', encoding='utf-8') as f:
                f.write(self.driver.page_source)
            print("✓ 当前页面源码已保存到 current_page.html")

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')

            # 查找layui表格的tbody
            tbody = soup.find('tbody')
            if not tbody:
                print("✗ 未找到表格tbody")
                return []

            # 查找所有数据行
            data_rows = tbody.find_all('tr')
            print(f"找到 {len(data_rows)} 行数据")

            for i, row in enumerate(data_rows):
                try:
                    journal_info = {}

                    # 根据实际HTML结构提取数据
                    # <td data-field="期刊全称">
                    期刊全称_td = row.find('td', {'data-field': '期刊全称'})
                    if 期刊全称_td:
                        div = 期刊全称_td.find('div', class_='layui-table-cell')
                        if div:
                            journal_info['期刊全称'] = self.clean_text(div.get_text())

                    # <td data-field="期刊简称">
                    期刊简称_td = row.find('td', {'data-field': '期刊简称'})
                    if 期刊简称_td:
                        div = 期刊简称_td.find('div', class_='layui-table-cell')
                        if div:
                            journal_info['期刊简称'] = self.clean_text(div.get_text())

                    # <td data-field="issn">
                    issn_td = row.find('td', {'data-field': 'issn'})
                    if issn_td:
                        div = issn_td.find('div', class_='layui-table-cell')
                        if div:
                            journal_info['ISSN'] = self.clean_text(div.get_text())

                    # <td data-field="学科大类">
                    学科大类_td = row.find('td', {'data-field': '学科大类'})
                    if 学科大类_td:
                        div = 学科大类_td.find('div', class_='layui-table-cell')
                        if div:
                            journal_info['学科大类'] = self.clean_text(div.get_text())

                    # <td data-field="分级">
                    分级_td = row.find('td', {'data-field': '分级'})
                    if 分级_td:
                        div = 分级_td.find('div', class_='layui-table-cell')
                        if div:
                            journal_info['分级'] = self.clean_text(div.get_text())

                    # 添加行索引
                    data_index = row.get('data-index', str(i))
                    journal_info['序号'] = data_index

                    # 只保存有期刊全称的记录
                    if journal_info.get('期刊全称'):
                        journals.append(journal_info)
                        if i < 5:  # 显示前5条用于调试
                            print(f"解析第{i+1}条: {journal_info.get('期刊全称', 'N/A')} - {journal_info.get('分级', 'N/A')}")

                except Exception as e:
                    print(f"解析第{i+1}行时出错: {e}")
                    continue

            print(f"✓ 成功解析 {len(journals)} 条期刊数据")
            return journals

        except Exception as e:
            print(f"✗ 解析数据失败: {e}")
            return []
    
    def clean_text(self, text):
        """清理文本"""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', text.strip())
    
    def handle_pagination(self):
        """处理分页，获取所有数据"""
        all_journals = []
        page_num = 1

        while True:
            print(f"正在处理第 {page_num} 页...")

            # 等待当前页数据加载
            self.wait_for_data_load()

            # 解析当前页数据
            current_page_journals = self.parse_journal_data()

            if not current_page_journals:
                print("当前页没有数据，停止翻页")
                break

            all_journals.extend(current_page_journals)
            print(f"第 {page_num} 页获取到 {len(current_page_journals)} 条数据")

            # 尝试翻到下一页
            try:
                # 查找下一页按钮
                next_button = self.driver.find_element(By.CSS_SELECTOR, ".layui-laypage-next")
                if "layui-disabled" in next_button.get_attribute("class"):
                    print("已到最后一页")
                    break

                next_button.click()
                print("正在翻到下一页...")
                time.sleep(3)  # 等待页面加载
                page_num += 1

            except NoSuchElementException:
                print("未找到下一页按钮，可能只有一页数据")
                break
            except Exception as e:
                print(f"翻页时出错: {e}")
                break

        return all_journals

    def crawl_all_journals(self, username, password):
        """爬取所有期刊数据"""
        try:
            # 设置浏览器
            if not self.setup_driver():
                return False

            # 登录
            if not self.login(username, password):
                return False

            # 处理分页并获取所有数据
            print("开始获取所有期刊数据...")
            all_journals = self.handle_pagination()
            self.journals_data.extend(all_journals)

            print(f"✓ 总共获取到 {len(self.journals_data)} 条期刊数据")
            return len(self.journals_data) > 0

        except Exception as e:
            print(f"✗ 爬取过程出错: {e}")
            return False
        finally:
            if self.driver:
                print("正在关闭浏览器...")
                self.driver.quit()
    
    def save_to_excel(self, filename="四川大学期刊数据.xlsx"):
        """保存到Excel"""
        try:
            if not self.journals_data:
                print("✗ 没有数据可保存")
                return False
            
            df = pd.DataFrame(self.journals_data)
            df.to_excel(filename, index=False, engine='openpyxl')
            print(f"✓ 数据已保存到 {filename}")
            print(f"✓ 共保存 {len(self.journals_data)} 条记录")
            
            # 显示预览
            print("\n数据预览:")
            print(df.head())
            
            return True
        except Exception as e:
            print(f"✗ 保存Excel失败: {e}")
            return False
    
    def save_to_csv(self, filename="四川大学期刊数据.csv"):
        """保存到CSV"""
        try:
            if not self.journals_data:
                return False
            
            df = pd.DataFrame(self.journals_data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✓ 数据已保存到 {filename}")
            return True
        except Exception as e:
            print(f"✗ 保存CSV失败: {e}")
            return False


def main():
    """主函数"""
    print("=" * 60)
    print("四川大学期刊查询系统爬虫")
    print("=" * 60)
    
    crawler = SCUJournalCrawler()
    
    # 获取登录信息
    print("\n请输入登录信息:")
    username = input("用户名（一卡通号）: ").strip()
    password = input("密码（图书馆借阅密码）: ").strip()
    
    if not username or not password:
        print("✗ 用户名和密码不能为空")
        return
    
    try:
        print("\n开始爬取期刊数据...")
        if crawler.crawl_all_journals(username, password):
            print("\n保存数据...")
            crawler.save_to_excel()
            crawler.save_to_csv()
            print("\n✓ 爬取完成！")
        else:
            print("\n✗ 爬取失败")
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n程序运行出错: {e}")


if __name__ == "__main__":
    main()
